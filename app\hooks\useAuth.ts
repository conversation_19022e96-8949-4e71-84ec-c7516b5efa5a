// hooks/useAuth.ts
import { useState, useEffect } from "react";

interface UserData {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  roleId: string;
  roleName: string;
  departmentId: string;
  departmentName: string;
  userType: string;
  name?: string;
  role?: { roleName: string };
  department?: { departmentName: string };
}

interface UseAuthReturn {
  user: UserData | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refetch: () => void;
}

// Helper function to get cookie value
const getCookie = (name: string): string | null => {
  if (typeof document === 'undefined') return null;
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refetch = () => {
    try {
      // First try to get user from localStorage (for direct login flow)
      let savedUser = localStorage.getItem("user");

      // If not in localStorage, check if we have a token in cookies and try to fetch user data
      if (!savedUser) {
        const token = getCookie("authToken");
        if (token) {
          // We have a token but no user data, this happens after page refresh
          // We'll need to fetch user data from the backend or decode the JWT
          // For now, let's try to decode the JWT to get basic user info
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            console.log("🔍 JWT payload:", payload);
            if (payload.sub && payload.email && payload.role) {
              // Create a basic user object from JWT payload
              const userFromToken: UserData = {
                userId: payload.sub,
                email: payload.email,
                firstName: payload.firstName || '',
                lastName: payload.lastName || '',
                roleId: '',
                roleName: payload.role,
                departmentId: '',
                departmentName: '',
                userType: payload.userType || '',
                // Add computed properties for backward compatibility
                name: `${payload.firstName || ''} ${payload.lastName || ''}`.trim() || payload.email,
                role: { roleName: payload.role },
                department: { departmentName: '' }
              };
              console.log("✅ User restored from JWT:", userFromToken);
              setUser(userFromToken);
              setLoading(false);
              return;
            }
          } catch (jwtError) {
            console.error("❌ Failed to decode JWT:", jwtError);
          }
        }
      }

      if (savedUser) {
        const parsedUser = JSON.parse(savedUser) as UserData;
        // Add computed properties for backward compatibility
        parsedUser.name = `${parsedUser.firstName} ${parsedUser.lastName}`.trim() || parsedUser.email;
        parsedUser.role = { roleName: parsedUser.roleName };
        parsedUser.department = { departmentName: parsedUser.departmentName };
        console.log("✅ User restored from localStorage:", parsedUser);
        setUser(parsedUser);
      } else {
        console.log("❌ No user found in localStorage");
        setUser(null);
      }
    } catch (err) {
      console.error("❌ Failed to restore user:", err);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refetch();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.message || "Login failed");
      }

      const data = await response.json();

      // ✅ matches backend keys exactly
      localStorage.setItem("token", data.token);
      localStorage.setItem("user", JSON.stringify(data.user));

      // Add computed properties for backward compatibility
      const userWithComputedProps = {
        ...data.user,
        name: `${data.user.firstName} ${data.user.lastName}`.trim() || data.user.email,
        role: { roleName: data.user.roleName },
        department: { departmentName: data.user.departmentName }
      };

      setUser(userWithComputedProps);
    } catch (err) {
      console.error("❌ Login error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    setUser(null);
  };

  return {
    user,
    loading,
    error,
    login,
    logout,
    refetch,
  };
};
