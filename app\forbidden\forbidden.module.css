.mainContainer {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fea918;
  padding: 2rem;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 2;
  margin-bottom: 2rem;
  margin-top: 5.5rem;
}

.title {
  font-weight: 800;
  text-transform: uppercase;
}

.imageContainer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.bottomImage {
  object-fit: contain;
}

.loginButton {
  background-color: #000000;
  &:hover {
    background-color: #6e152a;
  }
}
