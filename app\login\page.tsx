// "use client";

// import React, { useState } from "react";
// import {
//   Box,
//   Grid,
//   TextInput,
//   PasswordInput,
//   Button,
//   Title,
//   Modal,
//   Text,
// } from "@mantine/core";
// import { notifications } from "@mantine/notifications";

// // CSS Module Import
// import classes from "./login.module.css";

// // Image Imports
// import Image from "next/image";
// import loginBg from "../../public/login/advisory-login.png";
// import tslsLogo from "../../public/login/tsls-logo-stacked.png";
// //import loginMotif from "../../public/login/login-motif.png";

// // Animation Imports
// import loginAnimation from "../../public/login/login-animation.json";
// import Lottie from "lottie-react";

// // Icon Imports
// import { IconAt, IconPasswordUser, IconWalk } from "@tabler/icons-react";

// // Endpoint Imports
// import { LoginEndpoint } from "../utils/apiEndpoints";

// const Login = () => {
//   const [email, setEmail] = useState("");
//   const [password, setPassword] = useState("");
//   const [isLoading, setIsLoading] = useState(false);
//   const [showModal, setShowModal] = useState(false);
//   const [modalMessage, setModalMessage] = useState("");

//   // Regex for tsls.com.fj emails
//   const emailRegex = /^[^\s@]+@tsls\.com\.fj$/;

//   // Validation checks
//   const isEmailValid = emailRegex.test(email);
//   const isPasswordFilled = password.trim().length > 0;
//   const isFormValid = isEmailValid && isPasswordFilled;

//   const handleLogin = async () => {
//     if (!isFormValid) return;

//     setIsLoading(true);

//     try {
//       const response = await fetch(LoginEndpoint, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({
//           username: email,
//           password: password,
//         }),
//         credentials: "include",
//       });

//       const data = await response.json();

//       if (response.ok && data.token && data.user) {
//         // Normal login flow
//         // localStorage.setItem("authToken", data.token);

//         notifications.show({
//           title: "Login Successful!",
//           message: `Welcome back, ${data.user.name}!`,
//           color: "green",
//           autoClose: 5000,
//           withCloseButton: true,
//           styles: {
//             root: {
//               position: "fixed",
//               top: "1rem",
//               right: "1rem",
//               zIndex: 10000,
//             },
//           },
//         });

//         console.log("Login successful:", data);
//         // TODO: router.push('/dashboard');

//         // Redirect to dashboard
//         window.location.href = "/dashboard";
//       } else if (response.status === 403 && data.message) {
//         // ✅ User is authenticated but missing department/role
//         setModalMessage(data.message);
//         setShowModal(true);
//       } else {
//         // ❌ Invalid credentials
//         notifications.show({
//           title: "Login Failed!",
//           message: data.message || "Invalid credentials. Please try again.",
//           color: "red",
//           autoClose: 5000,
//           withCloseButton: true,
//           styles: {
//             root: {
//               position: "fixed",
//               top: "1rem",
//               right: "1rem",
//               zIndex: 10000,
//             },
//           },
//         });
//       }
//     } catch (error: any) {
//       console.error("Login error:", error);

//       notifications.show({
//         title: "Login Failed",
//         message: "Unexpected error. Please try again.",
//         color: "red",
//         autoClose: 5000,
//         withCloseButton: true,
//         styles: {
//           root: {
//             position: "fixed",
//             top: "1rem",
//             right: "1rem",
//             zIndex: 10000,
//           },
//         },
//       });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <Box className={classes.loginBg}>
//       <Grid gutter={0} className={classes.loginGrid}
//          style={{ marginLeft: "-420px" }} >
//         <Grid.Col span={2} style={{ textAlign: "center", marginBottom: 20 }}>
//           {/* <Image
//             src={loginMotif}
//             alt="login-motif"
//             style={{
//               width: "100%",
//               height: "auto",
//               objectFit: "cover",
//               marginLeft: 0,
//               marginTop: 15,
//             }}
//             priority
//           /> */}
//         </Grid.Col>

//         {/* Left Column: Form */}
//         <Grid.Col span={6} className={classes.formCol}>
//           <Image
//             src={tslsLogo}
//             alt="tsls-logo"
//             className={classes.tslsLogo}
//             priority
//           />
//           <Title order={5} className={classes.title}>
//             TSLS Partner Tertiary Education Institutions Portal
//           </Title>

//           <Box style={{ width: "100%" }} className={classes.loginForm}>
//             <TextInput
//               label="Email Address"
//               placeholder="<EMAIL>"
//               size="md"
//               radius="md"
//               mt="md"
//               w={"60%"}
//               value={email}
//               onChange={(event) => setEmail(event.currentTarget.value)}
//               error={
//                 email.length > 0 && !isEmailValid
//                   ? "Only @tsls.com.fj emails are allowed"
//                   : null
//               }
//               leftSectionPointerEvents="none"
//               leftSection={<IconAt size={16} />}
//               disabled={isLoading}
//             />

//             <PasswordInput
//               label="Password"
//               placeholder="Your password"
//               w={"60%"}
//               mt="md"
//               size="md"
//               radius="md"
//               value={password}
//               onChange={(event) => setPassword(event.currentTarget.value)}
//               leftSectionPointerEvents="none"
//               leftSection={<IconPasswordUser size={16} />}
//               disabled={isLoading}
//             />
//           </Box>

//           <Button
//             mt="xl"
//             size="md"
//             radius="md"
//             className={classes.loginButton}
//             rightSection={<IconWalk size={16} />}
//             disabled={!isFormValid || isLoading}
//             loading={isLoading}
//             onClick={handleLogin}
//             loaderProps={{ type: "dots" }}
//           >
//             LOGIN
//           </Button>
//         </Grid.Col>

//         {/* Right Column: Full Height Image */}
//         <Grid.Col span={4} className={classes.imageCol}>
//           <Image
//             src={loginBg}
//             alt="login-bg"
//             className={classes.advisoryLoginImg}
//             style={{ height: "90vh" }}
//             priority
//           />
//         </Grid.Col>
//       </Grid>

//       <Modal
//         opened={showModal}
//         onClose={() => setShowModal(false)}
//         centered
//         radius="md"
//       >
//         <Lottie
//           animationData={loginAnimation}
//           loop
//           style={{ width: 150, height: 150, margin: "0 auto" }}
//         />
//         <Text className={classes.modalTitle} size="xl">
//           Welcome!
//         </Text>
//         <Text className={classes.modalMessage}>{modalMessage}</Text>
//       </Modal>
//     </Box>
//   );
// };

// export default Login;

"use client";

import React, { useState } from "react";
import {
  Box,
  Grid,
  TextInput,
  PasswordInput,
  Button,
  Title,
  Modal,
  Text,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";

// CSS Module Import
import classes from "./login.module.css";

// Image Imports
import Image from "next/image";
import loginBg from "../../public/login/advisory-login.png";
import tslsLogo from "../../public/login/tsls-logo-stacked.png";
//import loginMotif from "../../public/login/login-motif.png";

// Animation Imports
import loginAnimation from "../../public/login/login-animation.json";
import Lottie from "lottie-react";

// Icon Imports
import { IconAt, IconPasswordUser, IconWalk } from "@tabler/icons-react";

// Endpoint Imports
import { LoginEndpoint } from "../utils/apiEndpoints";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");

  // Updated regex for general email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Validation checks
  const isEmailValid = emailRegex.test(email);
  const isPasswordFilled = password.trim().length > 0;
  const isFormValid = isEmailValid && isPasswordFilled;

const handleLogin = async () => {
  if (!isFormValid) return;

  setIsLoading(true);

  try {
    const response = await fetch(LoginEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: email,    
        password: password,
      }),
      credentials: "include",
    });

    const data = await response.json();

    if (response.ok && data.token && data.user) {
      // Store the JWT token as a cookie
      const secure = window.location.protocol === 'https:' ? '; secure' : '';
      document.cookie = `authToken=${data.token}; path=/; max-age=${8 * 60 * 60}; samesite=strict${secure}`;

      // Also store in localStorage for useAuth hook compatibility
      localStorage.setItem("token", data.token);
      localStorage.setItem("user", JSON.stringify(data.user));

      console.log("🔍 User data being stored:", data.user);
      console.log("🔍 User firstName:", data.user.firstName);
      console.log("🔍 User lastName:", data.user.lastName);
      console.log("🔍 User email:", data.user.email);

      notifications.show({
        title: "Login Successful!",
        message: `Welcome back, ${data.user.firstName || data.user.lastName}!`,
        color: "green",
        autoClose: 3000, // Reduced to 3 seconds
        withCloseButton: true,
        styles: {
          root: {
            position: "fixed",
            top: "1rem",
            right: "1rem",
            zIndex: 10000,
          },
        },
      });

      console.log("Login successful:", data);
      console.log("Cookie and localStorage set, redirecting to dashboard...");

      // Use a small delay to ensure data is stored
      setTimeout(() => {
        window.location.href = "/dashboard";
      }, 500);

    } else if (response.status === 403 && data.message) {
      setModalMessage(data.message);
      setShowModal(true);
    } else {
      notifications.show({
        title: "Login Failed!",
        message: data.error || "Invalid credentials. Please try again.",
        color: "red",
        autoClose: 5000,
        withCloseButton: true,
        styles: {
          root: {
            position: "fixed",
            top: "1rem",
            right: "1rem",
            zIndex: 10000,
          },
        },
      });
    }
  } catch (error: any) {
    console.error("Login error:", error);

    notifications.show({
      title: "Login Failed",
      message: "Unexpected error. Please try again.",
      color: "red",
      autoClose: 5000,
      withCloseButton: true,
      styles: {
        root: {
          position: "fixed",
          top: "1rem",
          right: "1rem",
          zIndex: 10000,
        },
      },
    });
  } finally {
    setIsLoading(false);
  }
};

  return (
    <Box className={classes.loginBg}>
      <Grid gutter={0} className={classes.loginGrid}
         style={{ marginLeft: "-420px" }} >
        <Grid.Col span={2} style={{ textAlign: "center", marginBottom: 20 }}>
          {/* <Image
            src={loginMotif}
            alt="login-motif"
            style={{
              width: "100%",
              height: "auto",
              objectFit: "cover",
              marginLeft: 0,
              marginTop: 15,
            }}
            priority
          /> */}
        </Grid.Col>

        {/* Left Column: Form */}
        <Grid.Col span={6} className={classes.formCol}>
          <Image
            src={tslsLogo}
            alt="tsls-logo"
            className={classes.tslsLogo}
            priority
          />
          <Title order={5} className={classes.title}>
            TSLS Partner Tertiary Education Institutions Portal
          </Title>

          <Box style={{ width: "100%" }} className={classes.loginForm}>
            <TextInput
              label="Email Address"
              placeholder="Enter your email address"
              size="md"
              radius="md"
              mt="md"
              w={"60%"}
              value={email}
              onChange={(event) => setEmail(event.currentTarget.value)}
              error={
                email.length > 0 && !isEmailValid
                  ? "Please enter a valid email address"
                  : null
              }
              leftSectionPointerEvents="none"
              leftSection={<IconAt size={16} />}
              disabled={isLoading}
            />

            <PasswordInput
              label="Password"
              placeholder="Your password"
              w={"60%"}
              mt="md"
              size="md"
              radius="md"
              value={password}
              onChange={(event) => setPassword(event.currentTarget.value)}
              leftSectionPointerEvents="none"
              leftSection={<IconPasswordUser size={16} />}
              disabled={isLoading}
            />
          </Box>

          <Button
            mt="xl"
            size="md"
            radius="md"
            className={classes.loginButton}
            rightSection={<IconWalk size={16} />}
            disabled={!isFormValid || isLoading}
            loading={isLoading}
            onClick={handleLogin}
            loaderProps={{ type: "dots" }}
          >
            LOGIN
          </Button>
        </Grid.Col>

        {/* Right Column: Full Height Image */}
        <Grid.Col span={4} className={classes.imageCol}>
          <Image
            src={loginBg}
            alt="login-bg"
            className={classes.advisoryLoginImg}
            style={{ height: "90vh" }}
            priority
          />
        </Grid.Col>
      </Grid>

      <Modal
        opened={showModal}
        onClose={() => setShowModal(false)}
        centered
        radius="md"
      >
        <Lottie
          animationData={loginAnimation}
          loop
          style={{ width: 150, height: 150, margin: "0 auto" }}
        />
        <Text className={classes.modalTitle} size="xl">
          Welcome!
        </Text>
        <Text className={classes.modalMessage}>{modalMessage}</Text>
      </Modal>
    </Box>
  );
};

export default Login;