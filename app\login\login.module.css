/* Outer container */
.loginBg {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* Grid takes full height */
.loginGrid {
  height: 100%;
}

/* Left column (form) */
.formCol {
  display: flex;
  flex-direction: column;
  justify-content: center; /* vertical center */
  align-items: center; /* horizontal center */
  padding: 40px;
}

/* Right column (image) */
.imageCol {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100vh;
  padding: 0;
  margin: 0;
}

/* Full height image without overflow */
.advisoryLoginImg {
  width: auto;
  height: 100vh;
  object-fit: contain;
}

.tslsLogo {
  width: 200px;
  height: auto;
  margin-bottom: 20px;
}

/* Animation above title */
.lottieLoginAnimation {
  width: 100%;
  max-width: 200px;
  height: auto;
  margin-bottom: 20px;
}

/* Title styling */
.title {
  margin-bottom: 30px;
  font-weight: 600;
}

.loginForm {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loginButton {
  background-color: #8f1d36;
  width: 30%;
  &:hover {
    background-color: #6e152a;
  }
}

.modalTitle {
  text-align: center;
  font-weight: 600;
  margin-top: 15px;
}

.modalMessage {
  text-align: center;
  margin-top: 5px;
}

/* Responsive for small screens */
@media (max-width: 768px) {
  .loginGrid {
    flex-direction: column;
  }
  .imageCol {
    display: none; /* hide image on small screens */
  }
}
