.tslsLogoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.logoutButton {
  /*color: #ffffff;*/
  background: transparent;
  &:hover {
    opacity: 0.8;
  }
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: flex-start;
  width: 100%;
}

.welcomeMessage {
  font-weight: 600;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: flex-end;
  width: 100%;
}

.navbar {
  /* background-color: #36454f; */
}

.topBorder {
  margin-top: 20px;
  rotate: 180deg;
  margin-bottom: -25px;
}

.sidebarSubtitle {
  opacity: 0.6;
}

.navButton {
  border-left: 4px solid transparent;
  justify-content: flex-start;
  border-radius: 0;
  color: #8f1d36;
  &:hover {
    border-left: 6px solid #8f1d36;
    background-color: rgba(143, 29, 54, 0.1);
    font-weight: 600;
    color: #8f1d36;
    border-radius: 5px;
  }
}

.active {
  border-left: 6px solid #8f1d36;
  background-color: rgba(143, 29, 54, 0.1);
  font-weight: 600;
  color: #8f1d36;
  border-radius: 5px;
}

.userMenuWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
}

.userMenuGroup {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.userName {
  opacity: 0;
  max-width: 0;
  overflow: hidden;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.showName {
  opacity: 1;
  max-width: 150px;
  margin-left: 6px;
}

/* On hover: shift avatar + reveal name */
.userMenuWrapper:hover .userMenuGroup {
  transform: translateX(-10px);
}

.userMenuWrapper:hover .userName {
  opacity: 1;
  max-width: 150px; /* adjust width for long names */
  margin-left: 6px;
}

.userEmail {
  opacity: 0;
  max-width: 0;
  overflow: hidden;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.showEmail {
  opacity: 1;
  max-width: 200px;
  margin-left: 6px;
}

.calendarWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  border: 2px solid #273d91;
  border-radius: 25px;
  padding: 2px 10px;
}

.calendarGroup {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 0.5rem;
}

.subNavButton {
  margin-top: 4px;
  font-size: 0.875rem;
  padding-left: 2rem;
  border-left: 1px solid #000;
}

.dropdownContainer {
  overflow: hidden;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out,
    padding 0.3s ease-in-out;
}

.dropdownOpen {
  max-height: 200px; /* Adjust based on number of items */
  opacity: 1;
  padding-top: 8px;
}

.dropdownClosed {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
}

.subNavButton {
  margin-top: 4px;
  font-size: 0.875rem;
  padding-left: 2rem;
  border-left: 1px solid rgba(143, 29, 54, 0.2);
  transition: all 0.2s ease;
}

/* Smooth chevron rotation */
.navButton svg {
  transition: transform 0.3s ease;
}

.scrollableSection {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(
    100vh - 200px
  ); /* Adjust based on your header and footer height */
  scrollbar-width: thin;
  scrollbar-color: rgba(143, 29, 54, 0.3) transparent;
}

/* Custom scrollbar styling for webkit browsers */
.scrollableSection::-webkit-scrollbar {
  width: 6px;
}

.scrollableSection::-webkit-scrollbar-track {
  background: transparent;
}

.scrollableSection::-webkit-scrollbar-thumb {
  background-color: rgba(143, 29, 54, 0.3);
  border-radius: 3px;
}

.scrollableSection::-webkit-scrollbar-thumb:hover {
  background-color: rgba(143, 29, 54, 0.5);
}
