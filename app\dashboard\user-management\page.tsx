"use client";

import React, { useState } from "react";
import {
  Title,
  Text,
  Card,
  Grid,
  Box,
  Button,
  TextInput,
  Select,
  Table,
  Badge,
  ActionIcon,
  Modal,
  Group,
  Stack,
  Alert,
  Pagination,
  Tooltip,
  useMantineTheme,
} from "@mantine/core";
import {
  IconUserPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconFilter,
  IconUserCog,
  IconInfoCircle,
} from "@tabler/icons-react";
import { useAuth } from "../../hooks/useAuth";

// Mock data for demonstration
const mockUsers = [
  {
    id: 1,
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    role: "University",
    department: "Computer Science",
    status: "Active",
    lastLogin: "2024-01-15",
  },
  {
    id: 2,
    firstName: "<PERSON>",
    lastName: "Smith",
    email: "<EMAIL>",
    role: "DepartmentAdmin",
    department: "Education Ministry",
    status: "Active",
    lastLogin: "2024-01-14",
  },
  {
    id: 3,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    role: "Officer",
    department: "Regional Office",
    status: "Inactive",
    lastLogin: "2024-01-10",
  },
  {
    id: 4,
    firstName: "Alice",
    lastName: "Brown",
    email: "<EMAIL>",
    role: "Student",
    department: "Engineering",
    status: "Active",
    lastLogin: "2024-01-16",
  },
];

const roleColors = {
  SuperAdmin: "red",
  DepartmentAdmin: "blue",
  Officer: "green",
  University: "purple",
  Student: "orange",
};

const statusColors = {
  Active: "green",
  Inactive: "red",
  Pending: "yellow",
};

export default function UserManagementPage() {
  const theme = useMantineTheme();
  const { user } = useAuth();
  const [users, setUsers] = useState(mockUsers);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newUser, setNewUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    role: "",
    department: "",
    status: "Active",
  });

  const itemsPerPage = 10;

  // Filter users based on search and filters
  const filteredUsers = users.filter((user) => {
    const matchesSearch = 
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = !roleFilter || user.role === roleFilter;
    const matchesStatus = !statusFilter || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

  const handleAddUser = () => {
    const id = Math.max(...users.map(u => u.id)) + 1;
    const userToAdd = {
      ...newUser,
      id,
      lastLogin: "Never",
    };
    setUsers([...users, userToAdd]);
    setNewUser({
      firstName: "",
      lastName: "",
      email: "",
      role: "",
      department: "",
      status: "Active",
    });
    setIsAddModalOpen(false);
  };

  const handleEditUser = () => {
    setUsers(users.map(u => u.id === selectedUser.id ? selectedUser : u));
    setIsEditModalOpen(false);
    setSelectedUser(null);
  };

  const handleDeleteUser = (userId) => {
    setUsers(users.filter(u => u.id !== userId));
  };

  const openEditModal = (user) => {
    setSelectedUser({ ...user });
    setIsEditModalOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setRoleFilter("");
    setStatusFilter("");
    setCurrentPage(1);
  };

  return (
    <Box p="md">
      <Grid>
        <Grid.Col span={12}>
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="lg">
              <Box>
                <Title order={2} c="#8F1D36">
                  <IconUserCog size={28} style={{ marginRight: 8 }} />
                  User Management
                </Title>
                <Text size="sm" c="dimmed" mt="xs">
                  Manage system users, roles, and permissions
                </Text>
              </Box>
              <Button
                leftSection={<IconUserPlus size={16} />}
                onClick={() => setIsAddModalOpen(true)}
                color="#8F1D36"
              >
                Add New User
              </Button>
            </Group>

            {/* Filters Section */}
            <Card withBorder p="md" mb="lg" bg="gray.0">
              <Group justify="space-between" mb="md">
                <Text fw={500} size="sm">
                  <IconFilter size={16} style={{ marginRight: 4 }} />
                  Filters & Search
                </Text>
                <Button variant="subtle" size="xs" onClick={clearFilters}>
                  Clear All
                </Button>
              </Group>
              
              <Grid>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <TextInput
                    placeholder="Search users..."
                    leftSection={<IconSearch size={16} />}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <Select
                    placeholder="Filter by role"
                    data={["SuperAdmin", "DepartmentAdmin", "Officer", "University", "Student"]}
                    value={roleFilter}
                    onChange={setRoleFilter}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <Select
                    placeholder="Filter by status"
                    data={["Active", "Inactive", "Pending"]}
                    value={statusFilter}
                    onChange={setStatusFilter}
                    clearable
                  />
                </Grid.Col>
              </Grid>
            </Card>

            {/* Users Table */}
            <Box style={{ overflowX: "auto" }}>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Name</Table.Th>
                    <Table.Th>Email</Table.Th>
                    <Table.Th>Role</Table.Th>
                    <Table.Th>Department</Table.Th>
                    <Table.Th>Status</Table.Th>
                    <Table.Th>Last Login</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedUsers.map((user) => (
                    <Table.Tr key={user.id}>
                      <Table.Td>
                        <Text fw={500}>{user.firstName} {user.lastName}</Text>
                      </Table.Td>
                      <Table.Td>{user.email}</Table.Td>
                      <Table.Td>
                        <Badge color={roleColors[user.role]} variant="light">
                          {user.role}
                        </Badge>
                      </Table.Td>
                      <Table.Td>{user.department}</Table.Td>
                      <Table.Td>
                        <Badge color={statusColors[user.status]} variant="light">
                          {user.status}
                        </Badge>
                      </Table.Td>
                      <Table.Td>{user.lastLogin}</Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="Edit User">
                            <ActionIcon
                              variant="subtle"
                              color="blue"
                              onClick={() => openEditModal(user)}
                            >
                              <IconEdit size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Delete User">
                            <ActionIcon
                              variant="subtle"
                              color="red"
                              onClick={() => handleDeleteUser(user.id)}
                            >
                              <IconTrash size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Box>

            {/* Pagination */}
            {totalPages > 1 && (
              <Group justify="center" mt="lg">
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  color="#8F1D36"
                />
              </Group>
            )}

            {/* Results Summary */}
            <Text size="sm" c="dimmed" mt="md" ta="center">
              Showing {paginatedUsers.length} of {filteredUsers.length} users
            </Text>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Add User Modal */}
      <Modal
        opened={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New User"
        size="md"
      >
        <Stack>
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="First Name"
                placeholder="Enter first name"
                value={newUser.firstName}
                onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Last Name"
                placeholder="Enter last name"
                value={newUser.lastName}
                onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                required
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Email"
            placeholder="Enter email address"
            value={newUser.email}
            onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
            required
          />

          <Select
            label="Role"
            placeholder="Select role"
            data={["SuperAdmin", "DepartmentAdmin", "Officer", "University", "Student"]}
            value={newUser.role}
            onChange={(value) => setNewUser({ ...newUser, role: value })}
            required
          />

          <TextInput
            label="Department"
            placeholder="Enter department"
            value={newUser.department}
            onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
            required
          />

          <Select
            label="Status"
            data={["Active", "Inactive", "Pending"]}
            value={newUser.status}
            onChange={(value) => setNewUser({ ...newUser, status: value })}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="subtle" onClick={() => setIsAddModalOpen(false)}>
              Cancel
            </Button>
            <Button color="#8F1D36" onClick={handleAddUser}>
              Add User
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit User"
        size="md"
      >
        {selectedUser && (
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="First Name"
                  value={selectedUser.firstName}
                  onChange={(e) => setSelectedUser({ ...selectedUser, firstName: e.target.value })}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Last Name"
                  value={selectedUser.lastName}
                  onChange={(e) => setSelectedUser({ ...selectedUser, lastName: e.target.value })}
                  required
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Email"
              value={selectedUser.email}
              onChange={(e) => setSelectedUser({ ...selectedUser, email: e.target.value })}
              required
            />

            <Select
              label="Role"
              data={["SuperAdmin", "DepartmentAdmin", "Officer", "University", "Student"]}
              value={selectedUser.role}
              onChange={(value) => setSelectedUser({ ...selectedUser, role: value })}
              required
            />

            <TextInput
              label="Department"
              value={selectedUser.department}
              onChange={(e) => setSelectedUser({ ...selectedUser, department: e.target.value })}
              required
            />

            <Select
              label="Status"
              data={["Active", "Inactive", "Pending"]}
              value={selectedUser.status}
              onChange={(value) => setSelectedUser({ ...selectedUser, status: value })}
            />

            <Group justify="flex-end" mt="md">
              <Button variant="subtle" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button color="#8F1D36" onClick={handleEditUser}>
                Save Changes
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Box>
  );
}
