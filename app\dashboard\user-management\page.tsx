"use client";

import React, { useState, useEffect } from "react";
import {
  Title,
  Text,
  Card,
  Grid,
  Box,
  Button,
  TextInput,
  Select,
  Table,
  Badge,
  ActionIcon,
  Modal,
  Group,
  Stack,
  Alert,
  Pagination,
  Tooltip,
  useMantineTheme,
  Loader,
  Center,
} from "@mantine/core";
import {
  IconUserPlus,
  IconEdit,
  IconTrash,
  IconSearch,
  IconFilter,
  IconUserCog,
  IconInfoCircle,
  IconLock,
  IconLockOpen,
  IconRefresh,
} from "@tabler/icons-react";
import { useAuth } from "../../hooks/useAuth";
import { UserEndpoints } from "../../utils/apiEndpoints";
import { notifications } from "@mantine/notifications";

// Types for API responses
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastLogin: string;
  isLocked?: boolean;
}

interface Role {
  id: string;
  name: string;
}

interface Department {
  id: string;
  name: string;
}

interface StatusOption {
  value: string;
  label: string;
}

const roleColors: Record<string, string> = {
  SuperAdmin: "red",
  DepartmentAdmin: "blue",
  Officer: "green",
  University: "purple",
  Student: "orange",
};

const statusColors: Record<string, string> = {
  Active: "green",
  Inactive: "red",
  Pending: "yellow",
};

export default function UserManagementPage() {
  const theme = useMantineTheme();
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [statusOptions, setStatusOptions] = useState<StatusOption[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string | null>("");
  const [statusFilter, setStatusFilter] = useState<string | null>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [userStats, setUserStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    locked: 0,
  });
  const [newUser, setNewUser] = useState({
    firstName: "",
    lastName: "",
    email: "",
    role: "",
    department: "",
    status: "Active",
  });

  const itemsPerPage = 10;

  // Load initial data
  useEffect(() => {
    console.log('🚀 UserManagement component mounted');
    console.log('🔧 API_BASE_URL:', process.env.NEXT_PUBLIC_API_URL);
    console.log('🔗 UserEndpoints.getUsers:', UserEndpoints.getUsers);
    loadUsers();
    loadRoles();
    loadDepartments();
    loadStatusOptions();
    loadUserStats();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading users from:', UserEndpoints.getUsers);
      const response = await fetch(UserEndpoints.getUsers, {
        credentials: 'include',
      });
      console.log('📡 Response status:', response.status);
      console.log('📡 Response ok:', response.ok);

      if (response.ok) {
        const data = await response.json();
        console.log('📦 Users data received:', data);
        console.log('📊 Number of users:', Array.isArray(data) ? data.length : 'Not an array');
        setUsers(data);
      } else {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        notifications.show({
          title: 'Error',
          message: `Failed to load users: ${response.status}`,
          color: 'red',
        });
      }
    } catch (error) {
      console.error('❌ Network Error loading users:', error);
      notifications.show({
        title: 'Error',
        message: 'Network error: Failed to load users',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      const response = await fetch(UserEndpoints.getRoles, {
        credentials: 'include',
      });
      if (response.ok) {
        const data = await response.json();
        setRoles(data);
      }
    } catch (error) {
      console.error('Error loading roles:', error);
    }
  };

  const loadDepartments = async () => {
    try {
      const response = await fetch(UserEndpoints.getDepartments, {
        credentials: 'include',
      });
      if (response.ok) {
        const data = await response.json();
        setDepartments(data);
      }
    } catch (error) {
      console.error('Error loading departments:', error);
    }
  };

  const loadStatusOptions = async () => {
    try {
      const response = await fetch(UserEndpoints.getStatusOptions, {
        credentials: 'include',
      });
      if (response.ok) {
        const data = await response.json();
        setStatusOptions(data);
      }
    } catch (error) {
      console.error('Error loading status options:', error);
    }
  };

  const loadUserStats = async () => {
    try {
      const response = await fetch(UserEndpoints.getUserStats, {
        credentials: 'include',
      });
      if (response.ok) {
        const data = await response.json();
        setUserStats(data);
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  // Filter users based on search and filters
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = !roleFilter || user.role === roleFilter;
    const matchesStatus = !statusFilter || user.status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

  const validateUser = (user: any) => {
    const errors: string[] = [];

    if (!user.firstName?.trim()) errors.push('First name is required');
    if (!user.lastName?.trim()) errors.push('Last name is required');
    if (!user.email?.trim()) errors.push('Email is required');
    if (!user.role?.trim()) errors.push('Role is required');
    if (!user.department?.trim()) errors.push('Department is required');

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (user.email && !emailRegex.test(user.email)) {
      errors.push('Please enter a valid email address');
    }

    return errors;
  };

  const handleAddUser = async () => {
    const validationErrors = validateUser(newUser);
    if (validationErrors.length > 0) {
      notifications.show({
        title: 'Validation Error',
        message: validationErrors.join(', '),
        color: 'red',
      });
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(UserEndpoints.createUser, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(newUser),
      });

      if (response.ok) {
        const createdUser = await response.json();
        setUsers([...users, createdUser]);
        setNewUser({
          firstName: "",
          lastName: "",
          email: "",
          role: "",
          department: "",
          status: "Active",
        });
        setIsAddModalOpen(false);
        loadUserStats(); // Refresh stats
        notifications.show({
          title: 'Success',
          message: 'User created successfully',
          color: 'green',
        });
      } else {
        const errorData = await response.json();
        notifications.show({
          title: 'Error',
          message: errorData.message || 'Failed to create user',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to create user',
        color: 'red',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditUser = async () => {
    if (!selectedUser) return;

    const validationErrors = validateUser(selectedUser);
    if (validationErrors.length > 0) {
      notifications.show({
        title: 'Validation Error',
        message: validationErrors.join(', '),
        color: 'red',
      });
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(UserEndpoints.updateUser(selectedUser.id), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(selectedUser),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUsers(users.map(u => u.id === selectedUser.id ? updatedUser : u));
        setIsEditModalOpen(false);
        setSelectedUser(null);
        loadUserStats(); // Refresh stats
        notifications.show({
          title: 'Success',
          message: 'User updated successfully',
          color: 'green',
        });
      } else {
        const errorData = await response.json();
        notifications.show({
          title: 'Error',
          message: errorData.message || 'Failed to update user',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error updating user:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update user',
        color: 'red',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      const response = await fetch(UserEndpoints.deleteUser(userId), {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        setUsers(users.filter(u => u.id !== userId));
        loadUserStats(); // Refresh stats
        notifications.show({
          title: 'Success',
          message: 'User deleted successfully',
          color: 'green',
        });
      } else {
        const errorData = await response.json();
        notifications.show({
          title: 'Error',
          message: errorData.message || 'Failed to delete user',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to delete user',
        color: 'red',
      });
    }
  };

  const handleToggleUserStatus = async (userId: string, newStatus: string) => {
    try {
      const response = await fetch(UserEndpoints.changeUserStatus(userId), {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUsers(users.map(u => u.id === userId ? updatedUser : u));
        loadUserStats(); // Refresh stats
        notifications.show({
          title: 'Success',
          message: 'User status updated successfully',
          color: 'green',
        });
      } else {
        const errorData = await response.json();
        notifications.show({
          title: 'Error',
          message: errorData.message || 'Failed to update user status',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update user status',
        color: 'red',
      });
    }
  };

  const handleUnlockUser = async (userId: string) => {
    try {
      const response = await fetch(UserEndpoints.unlockUserAccount(userId), {
        method: 'PATCH',
        credentials: 'include',
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUsers(users.map(u => u.id === userId ? updatedUser : u));
        loadUserStats(); // Refresh stats
        notifications.show({
          title: 'Success',
          message: 'User account unlocked successfully',
          color: 'green',
        });
      } else {
        const errorData = await response.json();
        notifications.show({
          title: 'Error',
          message: errorData.message || 'Failed to unlock user account',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Error unlocking user account:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to unlock user account',
        color: 'red',
      });
    }
  };

  const openEditModal = (user: User) => {
    setSelectedUser({ ...user });
    setIsEditModalOpen(true);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setRoleFilter("");
    setStatusFilter("");
    setCurrentPage(1);
  };

  return (
    <Box p="md">
      <Grid>
        <Grid.Col span={12}>
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="lg">
              <Box>
                <Title order={2} c="#8F1D36">
                  <IconUserCog size={28} style={{ marginRight: 8 }} />
                  User Management
                </Title>
                <Text size="sm" c="dimmed" mt="xs">
                  Manage system users, roles, and permissions
                </Text>
              </Box>
              <Group>
                <Button
                  variant="subtle"
                  leftSection={<IconRefresh size={16} />}
                  onClick={loadUsers}
                  loading={loading}
                >
                  Refresh
                </Button>
                <Button
                  leftSection={<IconUserPlus size={16} />}
                  onClick={() => setIsAddModalOpen(true)}
                  color="#8F1D36"
                >
                  Add New User
                </Button>
              </Group>
            </Group>

            {/* Stats Section */}
            <Grid mb="lg">
              <Grid.Col span={{ xs: 12, sm: 6, md: 3 }}>
                <Card withBorder p="md" bg="blue.0">
                  <Group justify="space-between">
                    <Box>
                      <Text size="xs" c="dimmed" fw={700} tt="uppercase">
                        Total Users
                      </Text>
                      <Text fw={700} size="xl">
                        {userStats.total}
                      </Text>
                    </Box>
                    <IconUserCog size={24} color="#8F1D36" />
                  </Group>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ xs: 12, sm: 6, md: 3 }}>
                <Card withBorder p="md" bg="green.0">
                  <Group justify="space-between">
                    <Box>
                      <Text size="xs" c="dimmed" fw={700} tt="uppercase">
                        Active Users
                      </Text>
                      <Text fw={700} size="xl" c="green">
                        {userStats.active}
                      </Text>
                    </Box>
                    <IconLockOpen size={24} color="green" />
                  </Group>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ xs: 12, sm: 6, md: 3 }}>
                <Card withBorder p="md" bg="red.0">
                  <Group justify="space-between">
                    <Box>
                      <Text size="xs" c="dimmed" fw={700} tt="uppercase">
                        Inactive Users
                      </Text>
                      <Text fw={700} size="xl" c="red">
                        {userStats.inactive}
                      </Text>
                    </Box>
                    <IconLock size={24} color="red" />
                  </Group>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ xs: 12, sm: 6, md: 3 }}>
                <Card withBorder p="md" bg="orange.0">
                  <Group justify="space-between">
                    <Box>
                      <Text size="xs" c="dimmed" fw={700} tt="uppercase">
                        Locked Accounts
                      </Text>
                      <Text fw={700} size="xl" c="orange">
                        {userStats.locked}
                      </Text>
                    </Box>
                    <IconLock size={24} color="orange" />
                  </Group>
                </Card>
              </Grid.Col>
            </Grid>

            {/* Filters Section */}
            <Card withBorder p="md" mb="lg" bg="gray.0">
              <Group justify="space-between" mb="md">
                <Text fw={500} size="sm">
                  <IconFilter size={16} style={{ marginRight: 4 }} />
                  Filters & Search
                </Text>
                <Button variant="subtle" size="xs" onClick={clearFilters}>
                  Clear All
                </Button>
              </Group>
              
              <Grid>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <TextInput
                    placeholder="Search users..."
                    leftSection={<IconSearch size={16} />}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <Select
                    placeholder="Filter by role"
                    data={roles.map(role => ({ value: role.name, label: role.name }))}
                    value={roleFilter}
                    onChange={(value) => setRoleFilter(value)}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ xs: 12, md: 4 }}>
                  <Select
                    placeholder="Filter by status"
                    data={statusOptions.map(status => ({ value: status.value, label: status.label }))}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value)}
                    clearable
                  />
                </Grid.Col>
              </Grid>
            </Card>

            {/* Users Table */}
            {loading ? (
              <Center py="xl">
                <Loader size="lg" />
              </Center>
            ) : (
              <Box style={{ overflowX: "auto" }}>
                <Table striped highlightOnHover>
                  <Table.Thead>
                    <Table.Tr>
                      <Table.Th>Name</Table.Th>
                      <Table.Th>Email</Table.Th>
                      <Table.Th>Role</Table.Th>
                      <Table.Th>Department</Table.Th>
                      <Table.Th>Status</Table.Th>
                      <Table.Th>Last Login</Table.Th>
                      <Table.Th>Actions</Table.Th>
                    </Table.Tr>
                  </Table.Thead>
                  <Table.Tbody>
                    {paginatedUsers.length === 0 ? (
                      <Table.Tr>
                        <Table.Td colSpan={7}>
                          <Center py="xl">
                            <Text c="dimmed">No users found</Text>
                          </Center>
                        </Table.Td>
                      </Table.Tr>
                    ) : (
                      paginatedUsers.map((user) => (
                        <Table.Tr key={user.id}>
                          <Table.Td>
                            <Text fw={500}>{user.firstName} {user.lastName}</Text>
                          </Table.Td>
                          <Table.Td>{user.email}</Table.Td>
                          <Table.Td>
                            <Badge color={roleColors[user.role] || "gray"} variant="light">
                              {user.role}
                            </Badge>
                          </Table.Td>
                          <Table.Td>{user.department}</Table.Td>
                          <Table.Td>
                            <Group gap="xs">
                              <Badge color={statusColors[user.status] || "gray"} variant="light">
                                {user.status}
                              </Badge>
                              {user.isLocked && (
                                <Badge color="red" variant="filled" size="xs">
                                  Locked
                                </Badge>
                              )}
                            </Group>
                          </Table.Td>
                          <Table.Td>{user.lastLogin}</Table.Td>
                          <Table.Td>
                            <Group gap="xs">
                              <Tooltip label="Edit User">
                                <ActionIcon
                                  variant="subtle"
                                  color="blue"
                                  onClick={() => openEditModal(user)}
                                >
                                  <IconEdit size={16} />
                                </ActionIcon>
                              </Tooltip>

                              {user.isLocked && (
                                <Tooltip label="Unlock Account">
                                  <ActionIcon
                                    variant="subtle"
                                    color="orange"
                                    onClick={() => handleUnlockUser(user.id)}
                                  >
                                    <IconLockOpen size={16} />
                                  </ActionIcon>
                                </Tooltip>
                              )}

                              <Tooltip label={user.status === 'Active' ? 'Deactivate User' : 'Activate User'}>
                                <ActionIcon
                                  variant="subtle"
                                  color={user.status === 'Active' ? 'red' : 'green'}
                                  onClick={() => handleToggleUserStatus(user.id, user.status === 'Active' ? 'Inactive' : 'Active')}
                                >
                                  {user.status === 'Active' ? <IconLock size={16} /> : <IconLockOpen size={16} />}
                                </ActionIcon>
                              </Tooltip>

                              <Tooltip label="Delete User">
                                <ActionIcon
                                  variant="subtle"
                                  color="red"
                                  onClick={() => handleDeleteUser(user.id)}
                                >
                                  <IconTrash size={16} />
                                </ActionIcon>
                              </Tooltip>
                            </Group>
                          </Table.Td>
                        </Table.Tr>
                      ))
                    )}
                  </Table.Tbody>
                </Table>
              </Box>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <Group justify="center" mt="lg">
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  color="#8F1D36"
                />
              </Group>
            )}

            {/* Results Summary */}
            <Text size="sm" c="dimmed" mt="md" ta="center">
              Showing {paginatedUsers.length} of {filteredUsers.length} users
            </Text>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Add User Modal */}
      <Modal
        opened={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add New User"
        size="md"
      >
        <Stack>
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label="First Name"
                placeholder="Enter first name"
                value={newUser.firstName}
                onChange={(e) => setNewUser({ ...newUser, firstName: e.target.value })}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label="Last Name"
                placeholder="Enter last name"
                value={newUser.lastName}
                onChange={(e) => setNewUser({ ...newUser, lastName: e.target.value })}
                required
              />
            </Grid.Col>
          </Grid>

          <TextInput
            label="Email"
            placeholder="Enter email address"
            value={newUser.email}
            onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
            required
          />

          <Select
            label="Role"
            placeholder="Select role"
            data={roles.map(role => ({ value: role.name, label: role.name }))}
            value={newUser.role}
            onChange={(value) => setNewUser({ ...newUser, role: value || "" })}
            required
          />

          <Select
            label="Department"
            placeholder="Select department"
            data={departments.map(dept => ({ value: dept.name, label: dept.name }))}
            value={newUser.department}
            onChange={(value) => setNewUser({ ...newUser, department: value || "" })}
            required
          />

          <Select
            label="Status"
            data={statusOptions.map(status => ({ value: status.value, label: status.label }))}
            value={newUser.status}
            onChange={(value) => setNewUser({ ...newUser, status: value || "Active" })}
          />

          <Group justify="flex-end" mt="md">
            <Button
              variant="subtle"
              onClick={() => setIsAddModalOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              color="#8F1D36"
              onClick={handleAddUser}
              loading={submitting}
            >
              Add User
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Edit User Modal */}
      <Modal
        opened={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit User"
        size="md"
      >
        {selectedUser && (
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label="First Name"
                  value={selectedUser.firstName}
                  onChange={(e) => setSelectedUser({ ...selectedUser, firstName: e.target.value })}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label="Last Name"
                  value={selectedUser.lastName}
                  onChange={(e) => setSelectedUser({ ...selectedUser, lastName: e.target.value })}
                  required
                />
              </Grid.Col>
            </Grid>

            <TextInput
              label="Email"
              value={selectedUser.email}
              onChange={(e) => setSelectedUser({ ...selectedUser, email: e.target.value })}
              required
            />

            <Select
              label="Role"
              data={roles.map(role => ({ value: role.name, label: role.name }))}
              value={selectedUser.role}
              onChange={(value) => setSelectedUser({ ...selectedUser, role: value || "" })}
              required
            />

            <Select
              label="Department"
              data={departments.map(dept => ({ value: dept.name, label: dept.name }))}
              value={selectedUser.department}
              onChange={(value) => setSelectedUser({ ...selectedUser, department: value || "" })}
              required
            />

            <Select
              label="Status"
              data={statusOptions.map(status => ({ value: status.value, label: status.label }))}
              value={selectedUser.status}
              onChange={(value) => setSelectedUser({ ...selectedUser, status: value || "Active" })}
            />

            <Group justify="flex-end" mt="md">
              <Button
                variant="subtle"
                onClick={() => setIsEditModalOpen(false)}
                disabled={submitting}
              >
                Cancel
              </Button>
              <Button
                color="#8F1D36"
                onClick={handleEditUser}
                loading={submitting}
              >
                Save Changes
              </Button>
            </Group>
          </Stack>
        )}
      </Modal>
    </Box>
  );
}
