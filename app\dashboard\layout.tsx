"use client";

import React from "react";
import {
  AppShell,
  Text,
  Button,
  Menu,
  Avatar,
  Group,
  Box,
  ActionIcon,
} from "@mantine/core";
import { useState, useEffect } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";

// Endpoint Imports
import { LogoutEndpoint } from "../utils/apiEndpoints";

import classes from "./dashboard.module.css";

// Image Imports
import tslsLogo from "../../public/login/tsls-logo-stacked.png";
import sidebarFooter from "../../public/dashboard/sidebar-footer.json";
import Lot<PERSON> from "lottie-react";

// Icon Imports
import {
  IconPower,
  IconCookieManFilled,
  IconBellFilled,
  IconBrandTinderFilled,
  IconLayoutDashboardFilled,
  IconReportAnalytics,
  IconUserCog,
  IconActivity,
  IconSettingsFilled,
  IconFileDownloadFilled,
} from "@tabler/icons-react";

// Hooks Imports
import { useAuth } from "../hooks/useAuth";

// // Reports menu items configuration
// const reportsMenuItems = [
//   { 
//     label: "All Enrolment Reports", 
//     href: "/dashboard/all-enrolment-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Tagging Reports", 
//     href: "/dashboard/all-tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Student Results Reports", 
//     href: "/dashboard/all-student-results-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Billing Reports", 
//     href: "/dashboard/all-billing-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Graduation Reports", 
//     href: "/dashboard/all-graduation-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All COP Tagging Reports", 
//     href: "/dashboard/all-cop-tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Enrolment Reports", 
//     href: "/dashboard/enrolment-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Tagging Reports", 
//     href: "/dashboard/tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Student Results Reports", 
//     href: "/dashboard/student-results-reports", 
//     icon: <IconReport size={16} />
//   },
// ];

// Role-based menu configuration
interface MenuItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  roles: string[]; // Roles that can access this menu item
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

// Define menu sections with role-based access
const menuSections: MenuSection[] = [
  {
    title: "Overview",
    items: [
      {
        label: "Dashboard",
        href: "/dashboard",
        icon: <IconLayoutDashboardFilled />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer", "Student", "University"]
      }
    ]
  },
  {
    title: "All Reports",
    items: [
      {
        label: "All Enrolment Reports",
        href: "/dashboard/all-enrolment-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer"]
      },
      {
        label: "All Tagging Reports",
        href: "/dashboard/all-tagging-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer"]
      },
      {
        label: "All Student Results Reports",
        href: "/dashboard/all-student-results-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer"]
      },
      {
        label: "All Billing Reports",
        href: "/dashboard/all-billing-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin"]
      },
      {
        label: "All Graduation Reports",
        href: "/dashboard/all-graduation-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer"]
      },
      {
        label: "All COP Tagging Reports",
        href: "/dashboard/all-cop-tagging-reports",
        icon: <IconReportAnalytics />,
        roles: ["SuperAdmin", "DepartmentAdmin", "Officer"]
      }
    ]
  },
  {
    title: "Individual Reports",
    items: [
      {
        label: "Enrolment Reports",
        href: "/dashboard/enrolment-reports",
        icon: <IconReportAnalytics />,
        roles: ["University", "Student"]
      },
      {
        label: "Tagging Reports",
        href: "/dashboard/tagging-reports",
        icon: <IconReportAnalytics />,
        roles: ["University", "Student"]
      },
      {
        label: "Student Results Reports",
        href: "/dashboard/student-results-reports",
        icon: <IconReportAnalytics />,
        roles: ["University", "Student"]
      }
    ]
  },
  {
    title: "Admin Actions",
    items: [
      {
        label: "User Management",
        href: "/dashboard/user-management",
        icon: <IconUserCog />,
        roles: ["SuperAdmin", "DepartmentAdmin"]
      },
      {
        label: "Activity Log",
        href: "/activity-log",
        icon: <IconActivity />,
        roles: ["SuperAdmin", "DepartmentAdmin"]
      },
      {
        label: "Lookups",
        href: "/lookups",
        icon: <IconSettingsFilled />,
        roles: ["SuperAdmin", "DepartmentAdmin"]
      },
      {
        label: "Reports Settings",
        href: "/reports-settings",
        icon: <IconFileDownloadFilled />,
        roles: ["SuperAdmin", "DepartmentAdmin"]
      }
    ]
  }
];

// Helper function to filter menu items based on user role
const getFilteredMenuSections = (userRole: string | undefined): MenuSection[] => {
  if (!userRole) return [];

  return menuSections.map(section => ({
    ...section,
    items: section.items.filter(item => item.roles.includes(userRole))
  })).filter(section => section.items.length > 0); // Remove empty sections
};

// export default function DashboardLayout({
//   children,
// }: {
//   children: React.ReactNode;
// }) {
//   const [opened, setOpened] = useState(false);
//   const router = useRouter();
//   const pathname = usePathname();

//   // Function to handle logout
//   const handleLogout = async () => {
//     try {
//       const response = await fetch(LogoutEndpoint, {
//         method: "POST",
//         credentials: "include",
//       });
//     } catch (error) {
//       console.error("Error logging out:", error);
//     }

//     // Force clear the cookie manually if logout is successful and auto logout fails
//     document.cookie =
//       "authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;";

//     window.location.href = "/login";
//   };

//   return (
//     <AppShell
//       header={{ height: 60 }}
//       navbar={{
//         width: 280,
//         breakpoint: "sm",
//         collapsed: { mobile: !opened },
//       }}
//       padding="md"
//     >
//       {/* Header */}
//       <AppShell.Header
//         style={{
//           display: "flex",
//           alignItems: "center",
//           justifyContent: "space-between",
//           padding: "0 1rem",
//           backgroundColor: "#36454F",
//         }}
//       >
//         <Image
//           src={tslsLogo}
//           alt="TSLS Logo"
//           width={360}
//           height={180}
//           className={classes.tslsLogo}
//         />
//         <Button
//           onClick={handleLogout}
//           className={classes.logoutButton}
//           leftSection={<IconPower size={16} />}
//         >
//           Logout
//         </Button>
//       </AppShell.Header>

//       {/* Sidebar */}
//       <AppShell.Navbar p="md">
//         <NavLink
//           label="Dashboard"
//           leftSection={<IconHomeFilled size={16} />}
//           active={pathname === "/dashboard"}
//           onClick={() => router.push("/dashboard")}
//           variant="subtle"
//           className={classes.navLink}
//         />
        
//         <Divider my="sm" label="Reports" labelPosition="left" />
        
//         {reportsMenuItems.map((item) => (
//           <NavLink
//             key={item.href}
//             label={item.label}
//             leftSection={item.icon}
//             active={pathname === item.href}
//             onClick={() => router.push(item.href)}
//             variant="subtle"
//             className={classes.navLink}
//           />
//         ))}
        
//         <Divider my="sm" label="Admin Actions" labelPosition="left" />
        
//         {adminMenuItems.map((item) => (
//           <NavLink
//             key={item.href}
//             label={item.label}
//             leftSection={item.icon}
//             active={pathname === item.href}
//             onClick={() => router.push(item.href)}
//             variant="subtle"
//             className={classes.navLink}
//           />
//         ))}
//       </AppShell.Navbar>

//       {/* Main content */}
//       <AppShell.Main>{children}</AppShell.Main>
//     </AppShell>
//   );
// }

// "use client";

// import { AppShell, Text, Button, NavLink, Divider, Avatar, Group, Menu, rem, ActionIcon } from "@mantine/core";
// import { useState } from "react";
// import Image from "next/image";
// import { useRouter, usePathname } from "next/navigation";

// // Endpoint Imports
// import { LogoutEndpoint } from "../utils/apiEndpoints";

// import classes from "./dashboard.module.css";

// // Image Imports
// import tslsLogo from "../../public/dashboard/tsls-logo-linear.png";

// // Icon Imports
// import { 
//   IconPower, 
//   IconHomeFilled,
//   IconReport, 
//   IconUserCog, 
//   IconUsers,
//   IconSchool,
//   IconBlocks,
//   IconSettingsPlus,
//   IconChevronDown,
//   IconUser,
//   IconBell,
//   IconSearch,
//   IconMessage,
//   IconSettings
// } from "@tabler/icons-react";

// // Reports menu items configuration
// const reportsMenuItems = [
//   { 
//     label: "All Enrolment Reports", 
//     href: "/dashboard/all-enrolment-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Tagging Reports", 
//     href: "/dashboard/all-tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Student Results Reports", 
//     href: "/dashboard/all-student-results-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Billing Reports", 
//     href: "/dashboard/all-billing-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All Graduation Reports", 
//     href: "/dashboard/all-graduation-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "All COP Tagging Reports", 
//     href: "/dashboard/all-cop-tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Enrolment Reports", 
//     href: "/dashboard/enrolment-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Tagging Reports", 
//     href: "/dashboard/tagging-reports", 
//     icon: <IconReport size={16} />
//   },
//   { 
//     label: "Student Results Reports", 
//     href: "/dashboard/student-results-reports", 
//     icon: <IconReport size={16} />
//   },
// ];






export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [opened, setOpened] = useState(false);
  const [advisoryDropdownOpen, setAdvisoryDropdownOpen] = useState(false);

  // Variables to store the user and loading state
  const { user, loading } = useAuth();

  console.log("🔍 Dashboard user data:", user);

  const displayName = user ? `${user.firstName} ${user.lastName}`.trim() || user.email || "Guest User" : "Guest User";
  const displayEmail = user?.email || "No email";
  const userRole = user?.role?.roleName || "No role";
  const userDepartment = user?.department?.departmentName || "No department";

  console.log("🔍 Display values:", { displayName, displayEmail, userRole, userDepartment });

  const WelcomeMessages = [
    // English
    "Welcome",
    "Hello",
    "Hi",
    "Hiya",
    "Howdy",
    "Ahoy!",
    "Ready to go",
    "Make today count",
    "Look who it is",
    "Let's crush it",
    "Good to see you",
    "What's up",
    "Rise and shine",
    "Let's get started",
    "Back at it",
    "Here we go",
    "Rock on",
    "Time to shine",
    "Looking sharp",
    "Glad you're here",
    "Let's roll",
    "All set?",
    "Let's make it happen",
    "Nice to have you back",
    "Full speed ahead",
    "Bula!",
    "नमस्ते!",
  ];

  // Pick a random welcome message on first render
  const [welcomeMessage, setWelcomeMessage] = useState("");

  // Get the current date and time
  const [now, setNow] = useState(new Date());
  useEffect(() => {
    const interval = setInterval(() => {
      setNow(new Date());
    }, 1000); // update every second
    return () => clearInterval(interval); // cleanup
  }, []);

  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * WelcomeMessages.length);
    setWelcomeMessage(WelcomeMessages[randomIndex]);
  }, []);

  const date = now.toLocaleDateString("en-US", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

  const time = now.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    second: "numeric", // include seconds if you want ticking clock
    hour12: true,
  });

  const [userMenuHovered, setUserMenuHovered] = useState(false);

  const pathname = usePathname();

  const isActive = (path: string) => pathname === path;

  // Check if any advisory route is active
  const isAdvisoryActive = () => {
    const advisoryPaths = [
      "/dashboard/advisory/tels",
      "/dashboard/advisory/scholarships-local",
      "/dashboard/advisory/nts-overseas",
      "/dashboard/advisory/masters-phds",
    ];
    return advisoryPaths.some((path) => pathname === path);
  };

  // Function to handle logout
  const handleLogout = async () => {
    try {
      const response = await fetch(LogoutEndpoint, {
        method: "POST",
        credentials: "include",
      });
    } catch (error) {
      console.error("Error logging out:", error);
    }

    // Clear both cookie and localStorage
    document.cookie =
      "authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC;";
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    window.location.href = "/login";
  };

  const getInitials = (name: string) => {
    if (!name) return "GU"; // fallback for Guest User
    const parts = name.trim().split(" ");
    if (parts.length === 1) return parts[0][0].toUpperCase();
    return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
  };

  const getFirstName = (name: string) => {
    if (!name) return "Guest";
    return name.trim().split(" ")[0];
  };

  const firstName = getFirstName(displayName);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <AppShell
      layout="alt"
      header={{ height: 60 }}
      navbar={{
        width: 280,
        breakpoint: "sm",
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      {/* Header */}
      <AppShell.Header
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "0 1rem",
        }}
      >
        {/* Jumble and show the welcome message */}
        <Box className={classes.headerLeft}>
          <Text size="md" className={classes.welcomeMessage}>
            👋 {welcomeMessage}, {firstName}
          </Text>
        </Box>
        <Box className={classes.headerRight}>
          {/* Calendar Section */}

          <Box className={classes.calendarWrapper}>
            <ActionIcon variant="transparent">
              <IconBrandTinderFilled size={24} color="#273D91" />
            </ActionIcon>
            <Box className={classes.calendarGroup}>
              <Text size="sm" color="#273D91">
                {date}
              </Text>

              <Text size="sm" color="#273D91">
                {time}
              </Text>
            </Box>
          </Box>

          <ActionIcon variant="transparent">
            <IconBellFilled size={24} color="#8F1D36" />
          </ActionIcon>

          {/* User Menu */}
          <Menu
            withArrow
            trigger="click-hover"
            openDelay={100}
            closeDelay={400}
          >
            <Menu.Target>
              <Box
                className={classes.userMenuWrapper}
                onMouseEnter={() => setUserMenuHovered(true)}
                onMouseLeave={() => setUserMenuHovered(false)}
              >
                <Group gap="sm" wrap="nowrap" className={classes.userMenuGroup}>
                  <Avatar size="md" radius="xl" color="#8F1D36">
                    {getInitials(displayName)}
                  </Avatar>
                  <Box className={classes.userDetails}>
                    <Text
                      size="sm"
                      className={`${classes.userName} ${
                        userMenuHovered ? classes.showName : ""
                      }`}
                    >
                      {displayName}
                    </Text>
                    <Text
                      size="xs"
                      color="dimmed"
                      className={`${classes.userEmail} ${
                        userMenuHovered ? classes.showEmail : ""
                      }`}
                    >
                      {displayEmail}
                    </Text>
                  </Box>
                </Group>
              </Box>
            </Menu.Target>

            <Menu.Dropdown
              onMouseEnter={() => setUserMenuHovered(true)}
              onMouseLeave={() => setUserMenuHovered(false)}
            >
              <Menu.Label>
                <Text size="xs">Application</Text>
              </Menu.Label>
              <Menu.Item leftSection={<IconCookieManFilled size={16} />}>
                View Profile
              </Menu.Item>
              <Menu.Divider />
              <Menu.Label>
                <Text size="xs">Danger Zone</Text>
              </Menu.Label>
              <Menu.Item
                onClick={handleLogout}
                leftSection={<IconPower size={16} />}
                color="red"
                variant="light"
              >
                Session Logout
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Box>
      </AppShell.Header>
      {/* Sidebar */}
      <AppShell.Navbar className={classes.navbar}>
        <AppShell.Section p={"xs"} className={classes.tslsLogoContainer}>
          <Image
            src={tslsLogo}
            alt="TSLS Logo"
            width={120}
            height={120}
            className={classes.tslsLogo}
          />
        </AppShell.Section>
        <AppShell.Section grow p={"xs"} className={classes.scrollableSection}>
          {getFilteredMenuSections(user?.roleName).map((section) => (
            <div key={section.title}>
              <Text
                size="xs"
                fw={500}
                mt={section.title === "Overview" ? "xs" : "md"}
                mb={"xs"}
                className={classes.sidebarSubtitle}
              >
                {section.title}
              </Text>
              {section.items.map((item) => (
                <Button
                  key={item.href}
                  variant={isActive(item.href) ? "light" : "subtle"}
                  fullWidth
                  justify="flex-start"
                  leftSection={item.icon}
                  className={`${classes.navButton} ${
                    isActive(item.href) ? classes.active : ""
                  }`}
                  onClick={() => (window.location.href = item.href)}
                >
                  {item.label}
                </Button>
              ))}
            </div>
          ))}
        </AppShell.Section>

        <AppShell.Section p={"xs"}>
          <Lottie animationData={sidebarFooter} loop={true} 
          style={{ width: 250, height: 145 }}/>
        </AppShell.Section>
      </AppShell.Navbar>
      {/* Main content */}
      <AppShell.Main>{children}</AppShell.Main>3
    </AppShell>
  );
}
