const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export const LoginEndpoint = `${API_BASE_URL}/api/auth/login`;
export const LogoutEndpoint = `${API_BASE_URL}/api/auth/logout`;

export const UserEndpoints = {
  getUsers: `${API_BASE_URL}/api/users`,
  getUserById: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  createUser: `${API_BASE_URL}/api/users`,
  updateUser: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  changeUserStatus: (userId: string) => `${API_BASE_URL}/api/users/${userId}/status`,
  unlockUserAccount: (userId: string) => `${API_BASE_URL}/api/users/${userId}/unlock`,
  deleteUser: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  getUserStats: `${API_BASE_URL}/api/users/stats`,
  getRoles: `${API_BASE_URL}/api/users/roles`,
  getDepartments: `${API_BASE_URL}/api/users/departments`,
  getStatusOptions: `${API_BASE_URL}/api/users/status-options`,
  getUserTypes: `${API_BASE_URL}/api/users/user-types`,
  resetPassword: `${API_BASE_URL}/api/users/reset-password`,
  verifyEmail: `${API_BASE_URL}/api/users/verify-email`,
};