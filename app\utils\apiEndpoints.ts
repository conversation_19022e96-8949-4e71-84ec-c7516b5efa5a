const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export const LoginEndpoint = `${API_BASE_URL}/api/auth/login`;
export const LogoutEndpoint = `${API_BASE_URL}/api/auth/logout`;

// User Management Endpoints
export const UserEndpoints = {
  // Get users with advanced filtering, searching, sorting, and pagination
  getUsers: `${API_BASE_URL}/api/users`,
  
  // Get user by ID
  getUserById: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  
  // Create new user
  createUser: `${API_BASE_URL}/api/users`,
  
  // Update user
  updateUser: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  
  // Change user status
  changeUserStatus: (userId: string) => `${API_BASE_URL}/api/users/${userId}/status`,
  
  // Unlock user account
  unlockUserAccount: (userId: string) => `${API_BASE_URL}/api/users/${userId}/unlock`,
  
  // Delete user
  deleteUser: (userId: string) => `${API_BASE_URL}/api/users/${userId}`,
  
  // Get user statistics
  getUserStats: `${API_BASE_URL}/api/users/stats`,
  
  // Get all active roles for dropdowns
  getRoles: `${API_BASE_URL}/api/users/roles`,
  
  // Get all active departments for dropdowns
  getDepartments: `${API_BASE_URL}/api/users/departments`,
  
  // Get available status options
  getStatusOptions: `${API_BASE_URL}/api/users/status-options`,
  
  // Get available user types
  getUserTypes: `${API_BASE_URL}/api/users/user-types`,
  
  // Password management
  resetPassword: `${API_BASE_URL}/api/users/reset-password`,
  verifyEmail: `${API_BASE_URL}/api/users/verify-email`,
};