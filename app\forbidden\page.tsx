"use client";

import { Title, Text, Button } from "@mantine/core";
import Link from "next/link";
import Image from "next/image";

// CSS Module Import
import classes from "./forbidden.module.css";

// Image Imports
import unauthorizedAccess from "../../public/forbidden/unauthorized-access.png";

export default function ForbiddenPage() {
  return (
    <div className={classes.mainContainer}>
      {/* Content */}
      <div className={classes.content}>
        <Title order={1} className={classes.title}>
          Unauthorized Access
        </Title>
        <Text className={classes.description}>
          You do not have permission to access this page. Please log in to
          continue.
        </Text>
        <Link href="/login">
          <Button mt="lg" size="md" className={classes.loginButton}>
            Go to Login
          </Button>
        </Link>
      </div>

      {/* Bottom Center Image */}
      <div className={classes.imageContainer}>
        <Image
          src={unauthorizedAccess}
          alt="Unauthorized access"
          className={classes.bottomImage}
          width={800}
          height={400}
        />
      </div>
    </div>
  );
}
