// Import styles of packages that you've installed.
import "@mantine/core/styles.css";

import {
  ColorSchemeScript,
  MantineProvider,
  mantineHtmlProps,
} from "@mantine/core";
import { Notifications } from "@mantine/notifications";
import { Poppins } from "next/font/google";

// Import Poppins font
const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"], // choose the weights you need
  display: "swap",
});

export const metadata = {
  title: "TSLS | Partner Tertiary Education Institutions Portal",
  description:
    "The Tertiary Scholarships and Loans Service (TSLS) Partner Tertiary Education Institutions Portal.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" {...mantineHtmlProps} className={poppins.className}>
      <head>
        <ColorSchemeScript />
      </head>
      <body>
        <MantineProvider
          theme={{
            fontFamily: "Poppins, sans-serif",
          }}
        >
          <Notifications position="top-right" zIndex={2000} />
          {children}
        </MantineProvider>
      </body>
    </html>
  );
}
